"use client";

import { useEffect, use } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/lib/stores/user-store";
import { useThemeStore } from "@/lib/stores/theme-store";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ExternalLink,
  Instagram,
  Youtube,
  Twitter,
  Globe,
  Heart
} from "lucide-react";

// Mock data - in real app this would come from database
const mockUserData = {
  alexcreator: {
    name: "<PERSON>",
    username: "alexcreator",
    bio: "Content creator & entrepreneur sharing tips about productivity, tech, and lifestyle. Join me on this journey! 🚀",
    avatar: "",
    template: "minimal",
    links: [
      { 
        id: "1", 
        title: "My YouTube Channel", 
        url: "https://youtube.com/@alexcreator", 
        icon: "youtube",
        description: "Weekly videos about productivity and tech"
      },
      { 
        id: "2", 
        title: "Instagram Profile", 
        url: "https://instagram.com/alexcreator", 
        icon: "instagram",
        description: "Daily inspiration and behind-the-scenes"
      },
      { 
        id: "3", 
        title: "Latest Blog Post", 
        url: "https://blog.alexcreator.com/latest", 
        icon: "globe",
        description: "How I Built My First SaaS in 30 Days"
      },
      { 
        id: "4", 
        title: "Shop My Products", 
        url: "https://shop.alexcreator.com", 
        icon: "external",
        description: "Productivity tools and courses"
      },
      { 
        id: "5", 
        title: "Free Newsletter", 
        url: "https://newsletter.alexcreator.com", 
        icon: "external",
        description: "Weekly tips delivered to your inbox"
      }
    ],
    socialLinks: [
      { platform: "twitter", url: "https://twitter.com/alexcreator" },
      { platform: "instagram", url: "https://instagram.com/alexcreator" },
      { platform: "youtube", url: "https://youtube.com/@alexcreator" }
    ]
  }
};

const getIconComponent = (iconType?: string) => {
  switch (iconType) {
    case "youtube":
      return <Youtube className="w-5 h-5" />;
    case "instagram":
      return <Instagram className="w-5 h-5" />;
    case "twitter":
      return <Twitter className="w-5 h-5" />;
    case "globe":
      return <Globe className="w-5 h-5" />;
    default:
      return <ExternalLink className="w-5 h-5" />;
  }
};

interface BioPageProps {
  params: {
    username: string;
  };
}

export default function BioPage({ params }: BioPageProps) {
  const { profile, links, socialLinks, incrementLinkClick, incrementPageView } = useUserStore();
  const { currentTheme } = useThemeStore();

  // For demo purposes, we'll use the store data if username matches, otherwise show mock data
  const isOwnPage = profile?.username === params.username;
  const userData = isOwnPage ? {
    name: profile.name,
    username: profile.username,
    bio: profile.bio,
    avatar: profile.avatar,
    template: profile.template,
    links: links.filter(link => link.isActive).sort((a, b) => a.order - b.order),
    socialLinks: socialLinks.filter(social => social.isVisible)
  } : mockUserData[params.username as keyof typeof mockUserData];

  // Get the current design settings
  const getDesignSettings = () => {
    if (isOwnPage && profile) {
      // Use profile design settings
      return {
        background: {
          type: profile.backgroundType || 'color',
          value: profile.backgroundType === 'image' ? profile.backgroundImage : profile.backgroundColor,
          customColor: profile.backgroundColor
        },
        typography: {
          fontFamily: profile.fontFamily || 'font-sans',
          fontSize: 16,
          textColor: 'text-white'
        },
        linkCards: {
          style: currentTheme?.linkCards?.style || 'glass',
          size: currentTheme?.linkCards?.size || 60
        },
        effects: {
          glassmorphism: profile.glassmorphism || false
        }
      };
    }
    // Default design for non-own pages
    return {
      background: {
        type: 'color',
        value: 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500'
      },
      typography: {
        fontFamily: 'font-sans',
        fontSize: 16,
        textColor: 'text-white'
      },
      linkCards: {
        style: 'glass',
        size: 60
      },
      effects: {
        glassmorphism: true
      }
    };
  };

  const design = getDesignSettings();

  useEffect(() => {
    // Track page view
    if (isOwnPage) {
      incrementPageView();
    }
  }, [isOwnPage, incrementPageView]);

  if (!userData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Page Not Found
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            The bio page you're looking for doesn't exist.
          </p>
        </div>
      </div>
    );
  }

  const handleLinkClick = (url: string, linkId?: string) => {
    // Track analytics if it's the user's own page
    if (isOwnPage && linkId) {
      incrementLinkClick(linkId);
    }
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  // Helper function to get background styles
  const getBackgroundStyles = () => {
    if (design.background.type === 'image' && design.background.value) {
      return {
        backgroundImage: `url(${design.background.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    } else if (design.background.type === 'color' && design.background.customColor) {
      return {
        backgroundColor: design.background.customColor
      };
    }
    return {};
  };

  // Helper function to get link card styles based on selected style
  const getLinkCardStyles = (style: string) => {
    switch (style) {
      case 'glass':
        return 'bg-white/20 backdrop-blur-lg border border-white/30 hover:bg-white/30';
      case 'neon':
        return 'bg-black border-2 border-cyan-400 shadow-lg shadow-cyan-400/50 hover:shadow-cyan-400/70';
      case 'minimal':
        return 'bg-white border border-gray-200 shadow-sm hover:shadow-md text-gray-900';
      case 'bold':
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 hover:from-purple-600 hover:to-pink-600';
      case 'soft':
        return 'bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-200 hover:from-blue-100 hover:to-purple-100 text-gray-900';
      case 'dark':
        return 'bg-gray-900 border border-gray-700 text-white hover:bg-gray-800';
      default:
        return 'bg-white/20 backdrop-blur-lg border border-white/30 hover:bg-white/30';
    }
  };

  return (
    <div
      className={`min-h-screen ${design.background.type === 'color' && !design.background.customColor ? design.background.value : ''}`}
      style={getBackgroundStyles()}
    >
      <div className="max-w-md mx-auto px-4 py-8">
        {/* Profile Section */}
        <div className={`text-center mb-8 ${design.typography.fontFamily}`}>
          <Avatar className="w-24 h-24 mx-auto mb-4 ring-4 ring-white/30 shadow-lg">
            {userData.avatar && <AvatarImage src={userData.avatar} />}
            <AvatarFallback className="text-2xl bg-gradient-to-br from-purple-500 to-pink-500 text-white">
              {userData.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>

          <h1 className={`text-2xl font-bold mb-2 ${design.typography.textColor}`}>
            {userData.name}
          </h1>

          <p className={`mb-4 leading-relaxed opacity-90 ${design.typography.textColor}`}>
            {userData.bio}
          </p>

          {/* Social Links */}
          <div className="flex justify-center space-x-4 mb-6">
            {userData.socialLinks.map((social, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="rounded-full w-10 h-10 p-0 bg-white/20 border-white/30 hover:bg-white/30 text-white"
                onClick={() => handleLinkClick(social.url)}
              >
                {getIconComponent(social.platform)}
              </Button>
            ))}
          </div>
        </div>

        {/* Links Section */}
        <div className="space-y-4 mb-8">
          {userData.links.map((link) => (
            <Card
              key={link.id}
              className={`p-4 transition-all duration-300 cursor-pointer rounded-xl ${getLinkCardStyles(design.linkCards.style)} ${design.typography.fontFamily}`}
              onClick={() => handleLinkClick(link.url, link.id)}
            >
              <div className="relative flex items-center justify-center">
                <div className="absolute left-3 w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white">
                  {getIconComponent(link.icon)}
                </div>
                <div className="flex-1 text-center">
                  <h3 className={`font-semibold ${design.linkCards.style === 'minimal' || design.linkCards.style === 'soft' ? 'text-gray-900' : 'text-white'}`}>
                    {link.title}
                  </h3>
                  {link.description && (
                    <p className={`text-sm opacity-80 ${design.linkCards.style === 'minimal' || design.linkCards.style === 'soft' ? 'text-gray-600' : 'text-white'}`}>
                      {link.description}
                    </p>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Footer */}
        <div className={`text-center ${design.typography.fontFamily}`}>
          <div className={`flex items-center justify-center space-x-2 text-sm mb-4 opacity-70 ${design.typography.textColor}`}>
            <Heart className="w-4 h-4" />
            <span>Made with</span>
            <Badge variant="secondary" className="bg-white/20 backdrop-blur-sm border-white/30 text-white">
              LinkVibe
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('/', '_blank')}
            className="text-xs bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            Create your own bio page
          </Button>
        </div>
      </div>
    </div>
  );
}
