"use client";

import { useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/lib/stores/user-store";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  ExternalLink, 
  Instagram, 
  Youtube, 
  Twitter,
  Globe,
  Heart
} from "lucide-react";

// Mock data - in real app this would come from database
const mockUserData = {
  alexcreator: {
    name: "<PERSON> Creator",
    username: "alexcreator",
    bio: "Content creator & entrepreneur sharing tips about productivity, tech, and lifestyle. Join me on this journey! 🚀",
    avatar: "",
    template: "minimal",
    links: [
      { 
        id: "1", 
        title: "My YouTube Channel", 
        url: "https://youtube.com/@alexcreator", 
        icon: "youtube",
        description: "Weekly videos about productivity and tech"
      },
      { 
        id: "2", 
        title: "Instagram Profile", 
        url: "https://instagram.com/alexcreator", 
        icon: "instagram",
        description: "Daily inspiration and behind-the-scenes"
      },
      { 
        id: "3", 
        title: "Latest Blog Post", 
        url: "https://blog.alexcreator.com/latest", 
        icon: "globe",
        description: "How I Built My First SaaS in 30 Days"
      },
      { 
        id: "4", 
        title: "Shop My Products", 
        url: "https://shop.alexcreator.com", 
        icon: "external",
        description: "Productivity tools and courses"
      },
      { 
        id: "5", 
        title: "Free Newsletter", 
        url: "https://newsletter.alexcreator.com", 
        icon: "external",
        description: "Weekly tips delivered to your inbox"
      }
    ],
    socialLinks: [
      { platform: "twitter", url: "https://twitter.com/alexcreator" },
      { platform: "instagram", url: "https://instagram.com/alexcreator" },
      { platform: "youtube", url: "https://youtube.com/@alexcreator" }
    ]
  }
};

const getIconComponent = (iconType: string) => {
  switch (iconType) {
    case "youtube":
      return <Youtube className="w-5 h-5" />;
    case "instagram":
      return <Instagram className="w-5 h-5" />;
    case "twitter":
      return <Twitter className="w-5 h-5" />;
    case "globe":
      return <Globe className="w-5 h-5" />;
    default:
      return <ExternalLink className="w-5 h-5" />;
  }
};

interface BioPageProps {
  params: {
    username: string;
  };
}

export default function BioPage({ params }: BioPageProps) {
  const { profile, links, socialLinks, incrementLinkClick, incrementPageView } = useUserStore();

  // For demo purposes, we'll use the store data if username matches, otherwise show mock data
  const isOwnPage = profile?.username === params.username;
  const userData = isOwnPage ? {
    name: profile.name,
    username: profile.username,
    bio: profile.bio,
    avatar: profile.avatar,
    template: profile.template,
    links: links.filter(link => link.isActive).sort((a, b) => a.order - b.order),
    socialLinks: socialLinks.filter(social => social.isVisible)
  } : mockUserData[params.username as keyof typeof mockUserData];

  useEffect(() => {
    // Track page view
    if (isOwnPage) {
      incrementPageView();
    }
  }, [isOwnPage, incrementPageView]);

  if (!userData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Page Not Found
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            The bio page you're looking for doesn't exist.
          </p>
        </div>
      </div>
    );
  }

  const handleLinkClick = (url: string, linkId?: string) => {
    // Track analytics if it's the user's own page
    if (isOwnPage && linkId) {
      incrementLinkClick(linkId);
    }
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      <div className="max-w-md mx-auto px-4 py-8">
        {/* Profile Section */}
        <div className="text-center mb-8">
          <Avatar className="w-24 h-24 mx-auto mb-4 ring-4 ring-white dark:ring-slate-800 shadow-lg">
            {userData.avatar && <AvatarImage src={userData.avatar} />}
            <AvatarFallback className="text-2xl bg-gradient-to-br from-purple-500 to-pink-500 text-white">
              {userData.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            {userData.name}
          </h1>
          
          <p className="text-slate-600 dark:text-slate-400 mb-4 leading-relaxed">
            {userData.bio}
          </p>

          {/* Social Links */}
          <div className="flex justify-center space-x-4 mb-6">
            {userData.socialLinks.map((social, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="rounded-full w-10 h-10 p-0"
                onClick={() => handleLinkClick(social.url)}
              >
                {getIconComponent(social.platform)}
              </Button>
            ))}
          </div>
        </div>

        {/* Links Section */}
        <div className="space-y-4 mb-8">
          {userData.links.map((link) => (
            <Card 
              key={link.id}
              className="p-4 hover:shadow-lg transition-all duration-300 cursor-pointer border-2 hover:border-purple-200 dark:hover:border-purple-700 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm"
              onClick={() => handleLinkClick(link.url, link.id)}
            >
              <div className="relative flex items-center justify-center">
                <div className="absolute left-3 w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white">
                  {getIconComponent(link.icon)}
                </div>
                <div className="flex-1 text-center">
                  <h3 className="font-semibold text-slate-900 dark:text-white">
                    {link.title}
                  </h3>
                  {link.description && (
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      {link.description}
                    </p>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Footer */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-500 dark:text-slate-400 mb-4">
            <Heart className="w-4 h-4" />
            <span>Made with</span>
            <Badge variant="secondary" className="bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 text-purple-700 dark:text-purple-300">
              LinkVibe
            </Badge>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => window.open('/', '_blank')}
            className="text-xs"
          >
            Create your own bio page
          </Button>
        </div>
      </div>
    </div>
  );
}
