"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import {
  Palette,
  Type,
  Image as ImageIcon,
  Upload,
  Brush,
  Sparkles,
  Eye,
  Download,
  Save,
  Heart
} from "lucide-react";
import { ThemeGallery } from "./theme-gallery";
import { FontSelector } from "./font-selector";
import { handleImageUpload, cleanupImagePreview, handleImageUploadToStorage } from "@/lib/image-utils";
import { LinkCardStyleSelector } from "./link-card-style-selector";
import { useThemeStore } from "@/lib/stores/theme-store";
import { useAuthStore } from "@/lib/stores/auth-store";
import { useUserStore } from "@/lib/stores/user-store";
import { useAppStore } from "@/lib/stores/app-store";

interface DesignCustomizerProps {
  onDesignChange: (design: any) => void;
  currentDesign: any;
}

export function DesignCustomizer({ onDesignChange, currentDesign }: DesignCustomizerProps) {
  const [selectedTab, setSelectedTab] = useState("background");
  const { setCurrentTheme } = useThemeStore();
  const { user } = useAuthStore();
  const { updateProfile } = useUserStore();
  const { addNotification } = useAppStore();

  const backgroundColors = [
    { name: "Gradient Purple", value: "bg-gradient-to-br from-purple-400 via-pink-500 to-red-500" },
    { name: "Ocean Blue", value: "bg-gradient-to-br from-blue-400 via-cyan-500 to-teal-500" },
    { name: "Sunset Orange", value: "bg-gradient-to-br from-orange-400 via-red-500 to-pink-500" },
    { name: "Forest Green", value: "bg-gradient-to-br from-green-400 via-emerald-500 to-teal-500" },
    { name: "Royal Purple", value: "bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500" },
    { name: "Golden Hour", value: "bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500" },
    { name: "Midnight", value: "bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900" },
    { name: "Cotton Candy", value: "bg-gradient-to-br from-pink-300 via-purple-300 to-indigo-300" }
  ];



  const handleBackgroundChange = (type: string, value: string) => {
    const newDesign = {
      ...currentDesign,
      background: {
        ...currentDesign.background,
        type,
        value
      }
    };

    // Update the design
    onDesignChange(newDesign);

    // Update the profile with proper background fields based on type
    if (type === 'color') {
      // When color is selected: set backgroundColor, clear backgroundImage
      updateProfile({
        backgroundType: 'color',
        backgroundColor: value,
        backgroundImage: '', // Clear image URL completely
        design: newDesign
      });

      // Show notification that changes need to be saved
      addNotification({
        type: 'info',
        title: 'Background Updated',
        message: 'Click "Save Changes" to save your background color to Appwrite.',
        duration: 3000
      });
    } else if (type === 'image') {
      // When image is selected: set backgroundImage, clear backgroundColor
      const imageUrl = value.includes('url(') ? value.match(/url\(([^)]+)\)/)?.[1] || value : value;

      updateProfile({
        backgroundType: 'image',
        backgroundImage: imageUrl,
        backgroundColor: '', // Clear color value completely
        design: newDesign
      });

      // Show notification for image
      if (value) {
        addNotification({
          type: 'info',
          title: 'Background Image Updated',
          message: 'Click "Save Changes" to save your background image to Appwrite.',
          duration: 3000
        });
      }
    } else if (type === 'none') {
      // When none is selected: clear both backgroundColor and backgroundImage
      updateProfile({
        backgroundType: 'none',
        backgroundColor: '',
        backgroundImage: '',
        design: newDesign
      });

      // Show notification for none
      addNotification({
        type: 'info',
        title: 'Background Removed',
        message: 'Click "Save Changes" to save your background settings to Appwrite.',
        duration: 3000
      });
    }
  };

  const handleFontChange = (fontFamily: string, fontName: string) => {
    onDesignChange({
      ...currentDesign,
      typography: {
        ...currentDesign.typography,
        fontFamily: fontFamily,
        fontName: fontName
      }
    });
  };

  const handleLinkStyleChange = (styleId: string, styleName: string) => {
    const newDesign = {
      ...currentDesign,
      linkCards: {
        ...currentDesign.linkCards,
        style: styleId,
        styleName: styleName
      }
    };
    onDesignChange(newDesign);
    setCurrentTheme(newDesign);
  };



  const handleThemeSelect = (theme: any) => {
    onDesignChange(theme);
    setCurrentTheme(theme);
  };

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-6">

        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid w-full grid-cols-4 bg-purple-100">
            <TabsTrigger value="background" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <ImageIcon className="w-4 h-4 mr-1" />
              Background
            </TabsTrigger>
            <TabsTrigger value="typography" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Type className="w-4 h-4 mr-1" />
              Fonts
            </TabsTrigger>
            <TabsTrigger value="links" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Brush className="w-4 h-4 mr-1" />
              Link Cards
            </TabsTrigger>
            <TabsTrigger value="effects" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Sparkles className="w-4 h-4 mr-1" />
              Effects
            </TabsTrigger>
          </TabsList>

          <TabsContent value="background" className="space-y-4 mt-6">
            <div>
              <Label className="text-sm font-semibold text-gray-700 mb-3 block">Background Type</Label>
              <div className="grid grid-cols-3 gap-2">
                <Button
                  variant={currentDesign.background?.type === 'color' ? 'default' : 'outline'}
                  onClick={() => {
                    // Switch to color mode and clear any existing values
                    handleBackgroundChange('color', currentDesign.background?.customColor || '#6366f1');
                  }}
                  className="h-auto p-3 flex flex-col items-center space-y-2"
                >
                  <Palette className="w-5 h-5" />
                  <span className="text-sm">Color/Gradient</span>
                </Button>
                <Button
                  variant={currentDesign.background?.type === 'image' ? 'default' : 'outline'}
                  onClick={() => {
                    // Switch to image mode and clear any existing values
                    handleBackgroundChange('image', '');
                  }}
                  className="h-auto p-3 flex flex-col items-center space-y-2"
                >
                  <Upload className="w-5 h-5" />
                  <span className="text-sm">Upload Image</span>
                </Button>
                <Button
                  variant={currentDesign.background?.type === 'none' ? 'default' : 'outline'}
                  onClick={() => {
                    // Switch to none mode and clear all background values
                    handleBackgroundChange('none', '');
                  }}
                  className="h-auto p-3 flex flex-col items-center space-y-2"
                >
                  <div className="w-5 h-5 border border-dashed border-gray-400 rounded-full" />
                  <span className="text-sm">None</span>
                </Button>
              </div>
            </div>

            {currentDesign.background?.type === 'color' && (
              <div className="space-y-4">
                {/* Color Picker for Solid Colors */}
                <div>
                  <Label className="text-sm font-semibold text-gray-700 mb-3 block">Solid Color</Label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      value={currentDesign.background?.customColor || "#6366f1"}
                      onChange={(e) => {
                        const color = e.target.value;

                        // Update the design object to store the custom color
                        const newDesign = {
                          ...currentDesign,
                          background: {
                            ...currentDesign.background,
                            customColor: color
                          }
                        };
                        onDesignChange(newDesign);

                        // Use the handleBackgroundChange function to update everything consistently
                        handleBackgroundChange('color', color);
                      }}
                      className="w-16 h-12 rounded-lg border-2 border-gray-200 cursor-pointer"
                      title="Choose custom background color"
                    />
                    <div className="flex-1">
                      <Label className="text-xs text-gray-600">Custom Color</Label>
                      <p className="text-xs text-gray-500">Click to choose any color</p>
                    </div>
                  </div>
                </div>

                {/* Gradient Presets */}
                <div>
                  <Label className="text-sm font-semibold text-gray-700 mb-3 block">Gradient Presets</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {backgroundColors.map((bg) => (
                      <button
                        key={bg.value}
                        onClick={() => {
                          handleBackgroundChange('color', bg.value);
                        }}
                        className={`h-16 rounded-lg ${bg.value} border-2 ${
                          currentDesign.background?.value === bg.value ? 'border-white shadow-lg' : 'border-transparent'
                        } hover:scale-105 transition-transform`}
                        title={bg.name}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}

            {currentDesign.background?.type === 'image' && (
              <div className="border-2 border-dashed border-purple-300 rounded-lg p-6 text-center">
                <Upload className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">Drop your image here or click to browse</p>

                {/* Show preview if available */}
                {currentDesign.background?.value && (
                  <div className="mb-4">
                    <div
                      className="w-full h-32 rounded-lg mx-auto mb-2 bg-cover bg-center"
                      style={{ backgroundImage: currentDesign.background.value }}
                    />
                    <p className="text-xs text-gray-500">
                      {currentDesign.background.value.includes('blob:')
                        ? 'Preview (will be uploaded when saved)'
                        : 'Uploaded image'}
                    </p>
                  </div>
                )}

                <input
                  type="file"
                  accept="image/*"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (file && user) {
                      // Clean up previous preview if exists
                      if (currentDesign.background?.value?.includes('blob:')) {
                        const urlMatch = currentDesign.background.value.match(/url\(([^)]+)\)/);
                        if (urlMatch) {
                          cleanupImagePreview(urlMatch[1]);
                        }
                      }

                      // First create a temporary preview for immediate display
                      await handleImageUpload(
                        file,
                        (previewUrl, validatedFile) => {
                          // Update UI with preview immediately (blob URL for preview)
                          handleBackgroundChange('image', `url(${previewUrl})`);

                          // Store the file for upload during save (don't upload immediately)
                          updateProfile({
                            backgroundImageFile: validatedFile,
                            backgroundType: 'image',
                            backgroundImage: previewUrl // Store preview URL temporarily
                          });

                          // Show info notification that image will be uploaded when saved
                          addNotification({
                            type: 'info',
                            title: 'Background Preview',
                            message: 'Background image will be uploaded when you click "Save Changes"',
                            duration: 3000
                          });
                        },
                        (error) => {
                          console.error('Background image upload error:', error);

                          // Show error notification to user
                          addNotification({
                            type: 'error',
                            title: 'Background Upload Error',
                            message: error || 'Failed to process background image',
                            duration: 5000
                          });
                        }
                      );
                    }
                  }}
                  className="hidden"
                  id="background-upload"
                />
                <Button
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700"
                  onClick={() => document.getElementById('background-upload')?.click()}
                >
                  Choose Image
                </Button>
              </div>
            )}

            {currentDesign.background?.type === 'none' && (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <div className="w-12 h-12 border-2 border-dashed border-gray-400 rounded-full mx-auto mb-2"></div>
                <p className="text-sm text-gray-600 mb-2">No background selected</p>
                <p className="text-xs text-gray-500">Your page will use the default background color</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="typography" className="space-y-4 mt-6">
            {/* Font Selector */}
            <FontSelector
              selectedFont={currentDesign.typography?.fontFamily}
              onFontSelect={handleFontChange}
              previewText={`${currentDesign.profile?.displayName || 'Your Name'} - Check out my links!`}
            />

            <div>
              <Label className="text-sm font-semibold text-gray-700 mb-3 block">Text Color</Label>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="color"
                    value={currentDesign.typography?.customTextColor || "#ffffff"}
                    onChange={(e) => onDesignChange({
                      ...currentDesign,
                      typography: {
                        ...currentDesign.typography,
                        customTextColor: e.target.value,
                        textColor: `[color:${e.target.value}]`
                      }
                    })}
                    className="w-12 h-10 rounded-lg border-2 border-gray-200 cursor-pointer"
                    title="Choose custom text color"
                  />
                  <div className="flex-1">
                    <Label className="text-xs text-gray-600">Custom Color</Label>
                    <p className="text-xs text-gray-500">Click to choose any color</p>
                  </div>
                </div>

                {/* Quick preset colors */}
                <div>
                  <Label className="text-xs text-gray-600 mb-2 block">Quick Presets</Label>
                  <div className="grid grid-cols-4 gap-2">
                    {[
                      { name: "White", value: "text-white", color: "#ffffff" },
                      { name: "Black", value: "text-black", color: "#000000" },
                      { name: "Gray", value: "text-gray-600", color: "#4b5563" },
                      { name: "Purple", value: "text-purple-600", color: "#9333ea" }
                    ].map((color) => (
                      <button
                        key={color.value}
                        onClick={() => onDesignChange({
                          ...currentDesign,
                          typography: {
                            ...currentDesign.typography,
                            textColor: color.value,
                            customTextColor: color.color
                          }
                        })}
                        className={`w-full h-8 rounded-lg border-2 ${
                          currentDesign.typography?.textColor === color.value
                            ? 'border-purple-500 shadow-lg'
                            : 'border-gray-200 hover:border-purple-300'
                        } transition-all`}
                        style={{ backgroundColor: color.color }}
                        title={color.name}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="links" className="space-y-4 mt-6">
            {/* Link Card Style Selector */}
            <LinkCardStyleSelector
              selectedStyleId={currentDesign.linkCards?.style}
              onStyleSelect={handleLinkStyleChange}
              previewText="Sample Link"
            />


          </TabsContent>

          <TabsContent value="effects" className="space-y-4 mt-6">
            <div className="text-center py-8">
              <Sparkles className="w-12 h-12 text-purple-400 mx-auto mb-4" />
              <h3 className="font-semibold text-gray-700 mb-2">Coming Soon!</h3>
              <p className="text-sm text-gray-500">
                Animations, particles, and interactive effects
              </p>
            </div>
          </TabsContent>
        </Tabs>


      </CardContent>
    </Card>
  );
}
