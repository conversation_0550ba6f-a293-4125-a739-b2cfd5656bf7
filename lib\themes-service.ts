import { supabase } from './supabase';
import { TABLES } from './supabase';

export interface ThemeDesign {
  background: {
    type: 'color' | 'image';
    value: string;
  };
  typography: {
    fontFamily: string;
    fontSize: number;
    textColor: string;
    customTextColor?: string;
  };
  linkCards: {
    style: string;
    size: number;
  };
  effects?: {
    animations: boolean;
    glassmorphism: boolean;
    shadows: boolean;
  };
}

export interface Theme {
  $id?: string;
  userId: string;
  name: string;
  description?: string;
  design: ThemeDesign;
  isPublic: boolean;
  isDefault: boolean;
  category: 'minimal' | 'dark' | 'neon' | 'pastel' | 'custom';
  tags: string[];
  createdAt: string;
  updatedAt: string;
  usageCount?: number;
}

export class ThemesService {
  // Save a new theme
  async saveTheme(theme: Omit<Theme, '$id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Theme> {
    try {
      const now = new Date().toISOString();
      const themeData = {
        ...theme,
        design: JSON.stringify(theme.design),
        tags: JSON.stringify(theme.tags),
        createdAt: now,
        updatedAt: now,
        usageCount: 0
      };

      const result = await databases.createDocument(
        DATABASE_ID,
        THEMES_COLLECTION_ID,
        ID.unique(),
        themeData
      );

      return {
        ...result,
        design: JSON.parse(result.design),
        tags: JSON.parse(result.tags)
      } as Theme;
    } catch (error) {
      console.error('Failed to save theme:', error);
      throw error;
    }
  }

  // Update an existing theme
  async updateTheme(themeId: string, updates: Partial<Theme>): Promise<Theme> {
    try {
      const updateData: any = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      if (updates.design) {
        updateData.design = JSON.stringify(updates.design);
      }
      if (updates.tags) {
        updateData.tags = JSON.stringify(updates.tags);
      }

      const result = await databases.updateDocument(
        DATABASE_ID,
        THEMES_COLLECTION_ID,
        themeId,
        updateData
      );

      return {
        ...result,
        design: JSON.parse(result.design),
        tags: JSON.parse(result.tags)
      } as Theme;
    } catch (error) {
      console.error('Failed to update theme:', error);
      throw error;
    }
  }

  // Get user's themes
  async getUserThemes(userId: string): Promise<Theme[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        THEMES_COLLECTION_ID,
        [
          Query.equal('userId', userId),
          Query.orderDesc('updatedAt'),
          Query.limit(50)
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        design: JSON.parse(doc.design),
        tags: JSON.parse(doc.tags)
      })) as Theme[];
    } catch (error) {
      console.error('Failed to get user themes:', error);
      throw error;
    }
  }

  // Get public themes
  async getPublicThemes(category?: string, limit: number = 20): Promise<Theme[]> {
    try {
      const queries = [
        Query.equal('isPublic', true),
        Query.orderDesc('usageCount'),
        Query.limit(limit)
      ];

      if (category) {
        queries.push(Query.equal('category', category));
      }

      const result = await databases.listDocuments(
        DATABASE_ID,
        THEMES_COLLECTION_ID,
        queries
      );

      return result.documents.map(doc => ({
        ...doc,
        design: JSON.parse(doc.design),
        tags: JSON.parse(doc.tags)
      })) as Theme[];
    } catch (error) {
      console.error('Failed to get public themes:', error);
      throw error;
    }
  }

  // Get theme by ID
  async getTheme(themeId: string): Promise<Theme> {
    try {
      const result = await databases.getDocument(
        DATABASE_ID,
        THEMES_COLLECTION_ID,
        themeId
      );

      return {
        ...result,
        design: JSON.parse(result.design),
        tags: JSON.parse(result.tags)
      } as Theme;
    } catch (error) {
      console.error('Failed to get theme:', error);
      throw error;
    }
  }

  // Delete theme
  async deleteTheme(themeId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        THEMES_COLLECTION_ID,
        themeId
      );
    } catch (error) {
      console.error('Failed to delete theme:', error);
      throw error;
    }
  }

  // Increment theme usage count
  async incrementUsage(themeId: string): Promise<void> {
    try {
      const theme = await this.getTheme(themeId);
      await this.updateTheme(themeId, {
        usageCount: (theme.usageCount || 0) + 1
      });
    } catch (error) {
      console.error('Failed to increment theme usage:', error);
      // Don't throw error for usage tracking
    }
  }

  // Search themes
  async searchThemes(query: string, isPublic: boolean = true): Promise<Theme[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        THEMES_COLLECTION_ID,
        [
          Query.equal('isPublic', isPublic),
          Query.search('name', query),
          Query.limit(20)
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        design: JSON.parse(doc.design),
        tags: JSON.parse(doc.tags)
      })) as Theme[];
    } catch (error) {
      console.error('Failed to search themes:', error);
      throw error;
    }
  }

  // Get default themes for each category
  async getDefaultThemes(): Promise<Theme[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        THEMES_COLLECTION_ID,
        [
          Query.equal('isDefault', true),
          Query.equal('isPublic', true),
          Query.orderAsc('category')
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        design: JSON.parse(doc.design),
        tags: JSON.parse(doc.tags)
      })) as Theme[];
    } catch (error) {
      console.error('Failed to get default themes:', error);
      throw error;
    }
  }

  // Create default themes (run once during setup)
  async createDefaultThemes(): Promise<void> {
    const defaultThemes: Omit<Theme, '$id' | 'createdAt' | 'updatedAt' | 'usageCount'>[] = [
      {
        userId: 'system',
        name: 'Minimal',
        description: 'Clean and simple design',
        design: {
          background: { type: 'color', value: 'bg-gradient-to-b from-white to-gray-50' },
          typography: { fontFamily: 'font-sans', fontSize: 16, textColor: 'text-black' },
          linkCards: { style: 'minimal', size: 60 }
        },
        isPublic: true,
        isDefault: true,
        category: 'minimal',
        tags: ['clean', 'simple', 'professional']
      },
      {
        userId: 'system',
        name: 'Dark Mode',
        description: 'Sleek dark theme',
        design: {
          background: { type: 'color', value: 'bg-gradient-to-b from-gray-900 to-black' },
          typography: { fontFamily: 'font-mono', fontSize: 16, textColor: 'text-white' },
          linkCards: { style: 'dark', size: 60 }
        },
        isPublic: true,
        isDefault: true,
        category: 'dark',
        tags: ['dark', 'modern', 'sleek']
      },
      {
        userId: 'system',
        name: 'Neon Glow',
        description: 'Vibrant neon effects',
        design: {
          background: { type: 'color', value: 'bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900' },
          typography: { fontFamily: 'font-mono', fontSize: 16, textColor: 'text-cyan-400' },
          linkCards: { style: 'neon', size: 60 }
        },
        isPublic: true,
        isDefault: true,
        category: 'neon',
        tags: ['neon', 'vibrant', 'futuristic']
      },
      {
        userId: 'system',
        name: 'Soft Pastel',
        description: 'Gentle pastel colors',
        design: {
          background: { type: 'color', value: 'bg-gradient-to-br from-pink-100 via-purple-50 to-indigo-100' },
          typography: { fontFamily: 'font-sans', fontSize: 16, textColor: 'text-gray-800' },
          linkCards: { style: 'soft', size: 60 }
        },
        isPublic: true,
        isDefault: true,
        category: 'pastel',
        tags: ['pastel', 'soft', 'gentle']
      }
    ];

    for (const theme of defaultThemes) {
      try {
        await this.saveTheme(theme);
      } catch (error) {
        console.error('Failed to create default theme:', theme.name, error);
      }
    }
  }
}

// Export singleton instance
export const themesService = new ThemesService();
