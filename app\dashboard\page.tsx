"use client";

import { useEffect, useState } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useUserStore } from "@/lib/stores/user-store";
import { useAppStore } from "@/lib/stores/app-store";
import { useAuthStore } from "@/lib/stores/auth-store";
import { useThemeStore } from "@/lib/stores/theme-store";
import { useLinksStore } from "@/lib/stores/links-store";
import { useProfileStore } from "@/lib/stores/profile-store";
import { useFontsStore } from "@/lib/stores/fonts-store";
import { AuthGuard } from "@/components/auth/auth-guard";
import { Notifications } from "@/components/notifications";

import { useDataSync } from "@/hooks/use-data-sync";
import { ProfileInitializer } from "@/components/profile-initializer";
import { handleImageUpload, cleanupImagePreview, handleImageUploadToStorage } from "@/lib/image-utils";
import { Button } from "@/components/ui/button";
import { DesignCustomizer } from "@/components/design-customizer";
import { IconSelector } from "@/components/icon-selector";
import { LivePreview } from "@/components/live-preview";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { TabSaveButton } from "@/components/tab-save-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Plus,
  Settings,
  Eye,
  BarChart3,
  Link as LinkIcon,
  Palette,
  Smartphone,
  Globe,
  GripVertical,
  Trash2,
  Edit3,
  LogOut,
  Save,
  Heart
} from "lucide-react";

// Remove mock data - now using Zustand stores

// Sortable Link Item Component
function SortableLinkItem({
  link,
  onUpdate,
  onDelete
}: {
  link: any;
  onUpdate: (id: string, updates: any) => void;
  onDelete: (id: string) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: link.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-start space-x-4 p-6 border-2 border-purple-200 rounded-xl hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-300 shadow-sm hover:shadow-md bg-white"
    >
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing"
      >
        <GripVertical className="w-5 h-5 text-gray-400" />
      </div>
      <div className="flex-1 space-y-3">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <Input
            value={link.title || ''}
            onChange={(e) => {
              // Immediate local update in the links store for preview
              const state = useLinksStore.getState();
              const updatedLinks = state.links.map(l =>
                l.id === link.id ? { ...l, title: e.target.value } : l
              );
              state.reorderLinks(updatedLinks);
            }}
            onBlur={(e) => {
              // Save to database when user leaves the field
              onUpdate(link.id, { title: e.target.value });
            }}
            placeholder="Link title"
            className="font-medium"
          />
          <Input
            value={link.url || ''}
            onChange={(e) => {
              // Immediate local update in the links store for preview
              const state = useLinksStore.getState();
              const updatedLinks = state.links.map(l =>
                l.id === link.id ? { ...l, url: e.target.value } : l
              );
              state.reorderLinks(updatedLinks);
            }}
            onBlur={(e) => {
              // Save to database when user leaves the field
              onUpdate(link.id, { url: e.target.value });
            }}
            placeholder="https://..."
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <label className="text-xs font-medium text-gray-600 mb-1 block">Icon</label>
            <IconSelector
              selectedIcon={link.icon || 'globe'}
              onIconSelect={(icon) => {
                // Immediate local update in the links store for preview
                const state = useLinksStore.getState();
                const updatedLinks = state.links.map(l =>
                  l.id === link.id ? { ...l, icon } : l
                );
                state.reorderLinks(updatedLinks);
              }}
            />
          </div>

          <div>
            <label className="text-xs font-medium text-gray-600 mb-1 block">Background Color</label>
            <div className="flex space-x-2">
              {[
                { color: '', label: 'None', className: 'bg-gray-100 border-dashed' },
                { color: 'bg-purple-500', label: 'Purple', className: 'bg-purple-500' },
                { color: 'bg-blue-500', label: 'Blue', className: 'bg-blue-500' },
                { color: 'bg-green-500', label: 'Green', className: 'bg-green-500' },
                { color: 'bg-red-500', label: 'Red', className: 'bg-red-500' },
                { color: 'bg-yellow-500', label: 'Yellow', className: 'bg-yellow-500' },
                { color: 'bg-pink-500', label: 'Pink', className: 'bg-pink-500' }
              ].map((colorOption) => (
                <button
                  key={colorOption.color || 'none'}
                  onClick={() => {
                    // Immediate local update in the links store for preview
                    const state = useLinksStore.getState();
                    const updatedLinks = state.links.map(l =>
                      l.id === link.id ? { ...l, backgroundColor: colorOption.color } : l
                    );
                    state.reorderLinks(updatedLinks);
                  }}
                  className={`w-8 h-8 rounded-lg ${colorOption.className} border-2 ${
                    link.backgroundColor === colorOption.color ? 'border-gray-800' : 'border-gray-300'
                  } hover:scale-110 transition-transform flex items-center justify-center`}
                  title={colorOption.label}
                >
                  {colorOption.color === '' && (
                    <span className="text-xs text-gray-500 font-bold">×</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="text-sm text-gray-500">
        {link.clicks} clicks
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onDelete(link.id)}
      >
        <Trash2 className="w-4 h-4" />
      </Button>
    </div>
  );
}

export default function Dashboard() {
  return (
    <AuthGuard>
      <DashboardContent />
    </AuthGuard>
  );
}

function DashboardContent() {
  // Auth store
  const { logout, user, userProfile } = useAuthStore();

  // New cached stores
  const linksStore = useLinksStore();
  const profileStore = useProfileStore();
  const fontsStore = useFontsStore();

  // Legacy stores (keeping for compatibility)
  const {
    activeTab,
    setActiveTab
  } = useUserStore();

  const {
    selectedTemplate,
    setSelectedTemplate,
    addNotification
  } = useAppStore();

  const { currentTheme, setCurrentTheme, saveTheme, isSaving } = useThemeStore();

  // Use cached data
  const profile = profileStore.profile || userProfile;
  const links = linksStore.links;
  const isLinksLoading = linksStore.isLoading;

  // Debug logging
  console.log('🔍 Dashboard - isLinksLoading:', isLinksLoading);
  console.log('🔍 Dashboard - links:', links);
  console.log('🔍 Dashboard - profile:', profile);

  // Design state - use profile design, theme store, or fallback to default
  const [design, setDesign] = useState(() => {
    // First try to use the design from the user profile
    if (profile?.design) {
      return profile.design;
    }

    // Then try to use the current theme from the theme store
    if (currentTheme) {
      return currentTheme;
    }

    // Fallback to default design
    return {
      background: {
        type: 'color' as 'color' | 'image',
        value: 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500'
      },
      typography: {
        fontFamily: 'font-sans',
        fontSize: 16,
        textColor: 'text-white',
        customTextColor: '#ffffff'
      },
      linkCards: {
        style: 'glass',
        size: 60
      }
    };
  });

  // Save theme state
  const [saveThemeName, setSaveThemeName] = useState("");
  const [saveThemeDescription, setSaveThemeDescription] = useState("");
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  // Update design when profile changes (after loading from Appwrite)
  useEffect(() => {
    if (profile?.design) {
      setDesign(profile.design);
      setCurrentTheme(profile.design);
    }
  }, [profile?.design, setCurrentTheme]);

  // Additional effect to handle profile background fields when design object is missing
  useEffect(() => {
    if (profile && !profile.design && (profile.backgroundType || profile.backgroundColor || profile.backgroundImage)) {
      const reconstructedDesign = {
        background: {
          type: profile.backgroundType || 'color',
          value: (() => {
            const bgType = profile.backgroundType || 'color';
            // Only use image if backgroundType is 'image' AND backgroundImage exists
            if (bgType === 'image' && profile.backgroundImage) {
              const imageUrl = profile.backgroundImage;
              return imageUrl.startsWith('url(') ? imageUrl : `url(${imageUrl})`;
            }
            // Only use color if backgroundType is 'color' AND backgroundColor exists
            else if (bgType === 'color' && profile.backgroundColor) {
              return profile.backgroundColor;
            }
            // Default fallback for color type
            else if (bgType === 'color') {
              return 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500';
            } else {
              return '';
            }
          })(),
          customColor: profile.backgroundType === 'color' ? profile.backgroundColor : undefined
        },
        typography: {
          fontFamily: profile.fontFamily || 'font-sans',
          fontSize: 16,
          textColor: 'text-white'
        },
        linkCards: {
          style: 'glass',
          size: 60
        },
        effects: {
          animations: true,
          glassmorphism: profile.glassmorphism || false,
          shadows: true
        }
      };

      setDesign(reconstructedDesign);
      setCurrentTheme(reconstructedDesign);

      // Update the profile with the reconstructed design
      if (profile?.id) {
        profileStore.updateProfile({ design: reconstructedDesign });
      }
    }
  }, [profile, setCurrentTheme]);

  // Load cached data on mount
  useEffect(() => {
    if (user?.id) {
      console.log('🔄 Loading cached data for user:', user.id);

      // Load profile data
      profileStore.loadProfile(user.id);

      // Load links data
      console.log('🔍 Dashboard - Loading links for user ID:', user.id);
      linksStore.setUserId(user.id);
      linksStore.loadLinks(user.id);

      // Load fonts (if not already loaded)
      if (fontsStore.allFonts.length === 0) {
        fontsStore.loadPopularFonts();
      }
    }
  }, [user?.id]);

  // Auto-refresh data periodically (every 10 minutes)
  useEffect(() => {
    if (!user?.id) return;

    const interval = setInterval(() => {
      console.log('🔄 Auto-refreshing cached data');
      profileStore.loadProfile(user.id, true);
      linksStore.loadLinks(user.id, true);
    }, 10 * 60 * 1000); // 10 minutes

    return () => clearInterval(interval);
  }, [user?.id]);

  // Template presets
  const templates = {
    minimal: {
      background: { type: 'color' as const, value: 'bg-gradient-to-b from-white to-gray-50' },
      typography: { fontFamily: 'font-sans', fontSize: 16, textColor: 'text-black', customTextColor: '#000000' },
      linkCards: { style: 'minimal', size: 60 }
    },
    dark: {
      background: { type: 'color' as const, value: 'bg-gradient-to-b from-gray-900 to-black' },
      typography: { fontFamily: 'font-mono', fontSize: 16, textColor: 'text-white', customTextColor: '#ffffff' },
      linkCards: { style: 'dark', size: 60 }
    },
    neon: {
      background: { type: 'color' as const, value: 'bg-gradient-to-br from-purple-900 via-pink-900 to-indigo-900' },
      typography: { fontFamily: 'font-sans', fontSize: 16, textColor: 'text-white', customTextColor: '#ffffff' },
      linkCards: { style: 'neon', size: 70 }
    },
    pastel: {
      background: { type: 'color' as const, value: 'bg-gradient-to-br from-pink-100 via-purple-100 to-blue-100' },
      typography: { fontFamily: 'font-sans', fontSize: 16, textColor: 'text-black', customTextColor: '#000000' },
      linkCards: { style: 'soft', size: 65 }
    }
  };

  // Handle design changes for real-time preview (don't save to Supabase immediately)
  const handleDesignChange = (newDesign: any) => {
    setDesign(newDesign);
    setCurrentTheme(newDesign);

    // Only update local state for real-time preview
    // Saving to Supabase will happen when user clicks "Save Changes" button
  };

  const applyTemplate = async (templateName: keyof typeof templates) => {
    const template = templates[templateName];
    handleDesignChange(template);

    // Auto-save template to themes collection
    if (user?.$id) {
      try {
        // Create a theme object with the correct structure
        const themeData = {
          userId: user.$id,
          name: `${templateName.charAt(0).toUpperCase() + templateName.slice(1)} Template`,
          description: `Quick template: ${templateName}`,
          design: template,
          isPublic: false,
          isDefault: false,
          category: templateName as 'minimal' | 'dark' | 'neon' | 'pastel',
          tags: ['quick-template', templateName]
        };

        await saveTheme(
          themeData.name,
          themeData.description,
          themeData.isPublic,
          themeData.userId,
          themeData.category,
          themeData.tags
        );

        addNotification({
          id: Date.now().toString(),
          type: 'success',
          message: `${templateName.charAt(0).toUpperCase() + templateName.slice(1)} template applied and saved!`,
          timestamp: new Date()
        });
      } catch (error) {
        console.error('Failed to save template:', error);
        addNotification({
          id: Date.now().toString(),
          type: 'success',
          message: `${templateName.charAt(0).toUpperCase() + templateName.slice(1)} template applied!`,
          timestamp: new Date()
        });
      }
    } else {
      addNotification({
        id: Date.now().toString(),
        type: 'success',
        message: `${templateName.charAt(0).toUpperCase() + templateName.slice(1)} template applied!`,
        timestamp: new Date()
      });
    }
  };

  const handleSaveTheme = async () => {
    if (!saveThemeName.trim()) return;
    if (!user?.id) {
      console.error('User not authenticated');
      return;
    }

    try {
      await saveTheme(saveThemeName, saveThemeDescription, false, user.id);
      setSaveThemeName("");
      setSaveThemeDescription("");
      setShowSaveDialog(false);
      addNotification({
        id: Date.now().toString(),
        type: 'success',
        message: 'Theme saved successfully!',
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Failed to save theme:', error);
      addNotification({
        id: Date.now().toString(),
        type: 'error',
        message: 'Failed to save theme. Please try again.',
        timestamp: new Date()
      });
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleAddNewLink = () => {
    if (!user?.id) return;

    // Create a temporary link with a temporary ID
    const tempLink = {
      id: `temp-${Date.now()}`, // Temporary ID
      title: "New Link",
      url: "https://",
      description: "",
      icon: "globe",
      backgroundColor: "", // Use camelCase for frontend consistency
      textColor: "",
      borderColor: "",
      customStyle: "",
      clicks: 0,
      isActive: true,
      order: links.length,
      user_id: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to local state only (not to Supabase yet)
    linksStore.addTempLink(tempLink);

    addNotification({
      type: 'success',
      title: 'Link Added',
      message: 'New link added to preview. Click "Save Changes" to save permanently.',
      duration: 3000,
    });
  };

  const handleDeleteLink = async (id: string) => {
    try {
      // Check if this is a temporary link
      if (id.startsWith('temp-')) {
        // Delete temporary link from local state only
        linksStore.deleteTempLink(id);
      } else {
        // Delete existing link from database
        await linksStore.deleteLink(id);
      }

      addNotification({
        type: 'success',
        title: 'Link Deleted',
        message: 'Link has been removed from your page',
        duration: 3000,
      });
    } catch (error) {
      console.error('Failed to delete link:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to delete link. Please try again.',
        duration: 3000,
      });
    }
  };

  const updateLink = async (id: string, updates: any) => {
    try {
      // Check if this is a temporary link
      if (id.startsWith('temp-')) {
        // Update temporary link in local state only
        linksStore.updateTempLink(id, updates);
      } else {
        // Update existing link in database
        await linksStore.updateLink(id, updates);
      }
    } catch (error) {
      console.error('Failed to update link:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to update link. Please try again.',
        duration: 3000,
      });
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = links.findIndex((item) => item.id === active.id);
      const newIndex = links.findIndex((item) => item.id === over?.id);

      const reorderedLinks = arrayMove(links, oldIndex, newIndex);
      linksStore.reorderLinks(reorderedLinks);

      // Save the new order in the background
      if (user?.id) {
        linksStore.saveAllLinks(user.id).catch(error => {
          console.error('Failed to save link order:', error);
        });
      }
    }
  };

  // Logout handler
  const handleLogout = async () => {
    try {
      await logout();
      // Redirect will be handled by AuthGuard
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Create a default profile for new users if none exists
  const currentProfile = profile || {
    id: user?.$id || '',
    name: user?.name || '',
    username: user?.email?.split('@')[0] || '',
    bio: '',
    avatar_url: '',
    email: user?.email || '',
    template: 'minimal' as const,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
      <ProfileInitializer />
      <Notifications />
      {/* Dashboard Header */}
      <header className="border-b bg-gradient-to-r from-purple-600 to-pink-600 text-white sticky top-0 z-50 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white">
                LinkVibe
              </h1>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 backdrop-blur-sm">Dashboard</Badge>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(`/${currentProfile.username}`, '_blank')}
                className="bg-white/20 border-white/30 text-white hover:bg-white/30"
              >
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </Button>
              <Button size="sm" className="bg-white/20 hover:bg-white/30 text-white border border-white/30">
                <Globe className="w-4 h-4 mr-2" />
                Publish
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="bg-white/20 border-white/30 text-white hover:bg-white/30"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
              <Avatar className="w-8 h-8">
                <AvatarImage src={currentProfile.avatar_url || null} />
                <AvatarFallback>{currentProfile.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200">
                <TabsTrigger value="builder" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-pink-600 data-[state=active]:text-white">
                  <Edit3 className="w-4 h-4 mr-2" />
                  Builder
                </TabsTrigger>
                <TabsTrigger value="design" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-pink-600 data-[state=active]:text-white">
                  <Palette className="w-4 h-4 mr-2" />
                  Design
                </TabsTrigger>
                <TabsTrigger value="analytics" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-pink-600 data-[state=active]:text-white">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Analytics
                </TabsTrigger>
              </TabsList>

              <TabsContent value="builder" className="space-y-6">
                {/* Profile Section */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-green-600 to-emerald-600 text-white">
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle className="flex items-center">
                          <Settings className="w-5 h-5 mr-2" />
                          Profile Information
                        </CardTitle>
                        <CardDescription className="text-green-100">
                          Customize your profile details and bio
                        </CardDescription>
                      </div>
                      <TabSaveButton
                        tabType="profile"
                        variant="outline"
                        size="sm"
                        className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                      />
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <Avatar className="w-16 h-16">
                        <AvatarImage src={currentProfile.avatar_url || null} />
                        <AvatarFallback className="text-lg">
                          {currentProfile.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              // Clean up previous preview if exists
                              if (currentProfile.avatar_url?.startsWith('blob:')) {
                                cleanupImagePreview(currentProfile.avatar_url);
                              }

                              // Handle image upload with validation - only create preview
                              await handleImageUpload(
                                file,
                                (previewUrl, validatedFile) => {
                                  // Store the preview URL and file for later upload
                                  profileStore.setProfile({
                                    ...profile!,
                                    avatar_url: previewUrl,
                                    avatarFile: validatedFile
                                  });
                                  profileStore.markUnsavedChanges(true);
                                },
                                (error) => {
                                  console.error('Avatar upload error:', error);
                                  addNotification({
                                    type: 'error',
                                    title: 'Avatar Upload Failed',
                                    message: error || 'Failed to process avatar image',
                                    duration: 5000,
                                  });
                                }
                              );
                            }
                          }}
                          className="hidden"
                          id="avatar-upload"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById('avatar-upload')?.click()}
                        >
                          Change Photo
                        </Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Display Name</label>
                        <Input
                          value={currentProfile.name}
                          onChange={(e) => {
                            profileStore.markUnsavedChanges(true);
                            // Update local state immediately for responsiveness
                            profileStore.setProfile({ ...profile!, name: e.target.value });
                          }}
                          onBlur={(e) => {
                            // Save on blur
                            if (profile?.id) {
                              profileStore.updateProfile({ name: e.target.value });
                            }
                          }}
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">Username</label>
                        <Input
                          value={currentProfile.username}
                          onChange={(e) => {
                            profileStore.markUnsavedChanges(true);
                            profileStore.setProfile({ ...profile!, username: e.target.value });
                          }}
                          onBlur={(e) => {
                            if (profile?.id) {
                              profileStore.updateProfile({ username: e.target.value });
                            }
                          }}
                          placeholder="your-username"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Bio</label>
                      <Textarea
                        value={currentProfile.bio}
                        onChange={(e) => {
                          profileStore.markUnsavedChanges(true);
                          profileStore.setProfile({ ...profile!, bio: e.target.value });
                        }}
                        onBlur={(e) => {
                          if (profile?.id) {
                            profileStore.updateProfile({ bio: e.target.value });
                          }
                        }}
                        placeholder="Tell people about yourself..."
                        rows={3}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Links Section */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white">
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle className="flex items-center">
                          <LinkIcon className="w-5 h-5 mr-2" />
                          Your Links
                        </CardTitle>
                        <CardDescription className="text-blue-100">
                          Add and organize your important links
                        </CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button onClick={handleAddNewLink} size="sm">
                          <Plus className="w-4 h-4 mr-2" />
                          Add Link
                        </Button>
                        <TabSaveButton
                          tabType="links"
                          variant="outline"
                          size="sm"
                          className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                        />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {isLinksLoading ? (
                      <div className="flex flex-col items-center justify-center py-12 space-y-4">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p className="text-sm text-gray-500">Loading your links...</p>
                      </div>
                    ) : links.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-12 space-y-4">
                        <LinkIcon className="w-12 h-12 text-gray-300" />
                        <div className="text-center">
                          <p className="text-gray-500 mb-2">No links yet</p>
                          <p className="text-sm text-gray-400">Click "Add Link" to create your first link</p>
                        </div>
                      </div>
                    ) : (
                      <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={handleDragEnd}
                      >
                        <SortableContext
                          items={links.map(link => link.id)}
                          strategy={verticalListSortingStrategy}
                        >
                          <div className="space-y-4">
                            {links.map((link) => (
                              <SortableLinkItem
                                key={link.id}
                                link={link}
                                onUpdate={updateLink}
                                onDelete={handleDeleteLink}
                              />
                            ))}
                          </div>
                        </SortableContext>
                      </DndContext>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="design" className="space-y-6">
                {/* Design Save Button */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle className="flex items-center">
                          <Palette className="w-5 h-5 mr-2" />
                          Design Customization
                        </CardTitle>
                        <CardDescription className="text-purple-100">
                          Customize your page appearance and styling
                        </CardDescription>
                      </div>
                      <TabSaveButton
                        tabType="design"
                        variant="outline"
                        size="sm"
                        className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                      />
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <DesignCustomizer
                      onDesignChange={handleDesignChange}
                      currentDesign={design}
                    />
                  </CardContent>
                </Card>

                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-orange-600 to-yellow-600 text-white">
                    <CardTitle>Quick Templates</CardTitle>
                    <CardDescription className="text-orange-100">
                      Start with a pre-designed template
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Minimal Template */}
                      <div
                        className="cursor-pointer border-2 border-gray-200 hover:border-purple-300 rounded-lg p-4 transition-all hover:shadow-lg"
                        onClick={() => applyTemplate('minimal')}
                      >
                        <div className="aspect-[3/4] bg-gradient-to-b from-white to-gray-50 p-4 rounded-lg mb-3 flex flex-col items-center justify-center">
                          <div className="w-12 h-12 bg-gray-200 rounded-full mb-3"></div>
                          <div className="w-16 h-2 bg-gray-300 rounded mb-1"></div>
                          <div className="w-12 h-1 bg-gray-200 rounded mb-4"></div>
                          <div className="space-y-2 w-full">
                            <div className="w-full h-6 bg-gray-100 rounded"></div>
                            <div className="w-full h-6 bg-gray-100 rounded"></div>
                            <div className="w-full h-6 bg-gray-100 rounded"></div>
                          </div>
                        </div>
                        <h3 className="font-semibold text-center">Minimal</h3>
                        <p className="text-sm text-gray-600 text-center">Clean and professional</p>
                      </div>

                      {/* Dark Mode Template */}
                      <div
                        className="cursor-pointer border-2 border-gray-200 hover:border-purple-300 rounded-lg p-4 transition-all hover:shadow-lg"
                        onClick={() => applyTemplate('dark')}
                      >
                        <div className="aspect-[3/4] bg-gradient-to-b from-gray-900 to-black p-4 rounded-lg mb-3 flex flex-col items-center justify-center">
                          <div className="w-12 h-12 bg-gray-700 rounded-full mb-3"></div>
                          <div className="w-16 h-2 bg-gray-600 rounded mb-1"></div>
                          <div className="w-12 h-1 bg-gray-700 rounded mb-4"></div>
                          <div className="space-y-2 w-full">
                            <div className="w-full h-6 bg-gray-800 rounded"></div>
                            <div className="w-full h-6 bg-gray-800 rounded"></div>
                            <div className="w-full h-6 bg-gray-800 rounded"></div>
                          </div>
                        </div>
                        <h3 className="font-semibold text-center">Dark Mode</h3>
                        <p className="text-sm text-gray-600 text-center">Sleek and modern</p>
                      </div>

                      {/* Neon Template */}
                      <div
                        className="cursor-pointer border-2 border-gray-200 hover:border-purple-300 rounded-lg p-4 transition-all hover:shadow-lg"
                        onClick={() => applyTemplate('neon')}
                      >
                        <div className="aspect-[3/4] bg-gradient-to-b from-purple-900 via-pink-900 to-black p-4 rounded-lg mb-3 flex flex-col items-center justify-center">
                          <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full mb-3 shadow-lg shadow-cyan-500/25"></div>
                          <div className="w-16 h-2 bg-gradient-to-r from-cyan-400 to-pink-400 rounded mb-1"></div>
                          <div className="w-12 h-1 bg-purple-400 rounded mb-4"></div>
                          <div className="space-y-2 w-full">
                            <div className="w-full h-6 bg-gradient-to-r from-cyan-500 to-pink-500 rounded shadow-lg shadow-cyan-500/25"></div>
                            <div className="w-full h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded shadow-lg shadow-purple-500/25"></div>
                            <div className="w-full h-6 bg-gradient-to-r from-pink-500 to-orange-500 rounded shadow-lg shadow-pink-500/25"></div>
                          </div>
                        </div>
                        <h3 className="font-semibold text-center">Neon</h3>
                        <p className="text-sm text-gray-600 text-center">Bold and vibrant</p>
                      </div>

                      {/* Pastel Template */}
                      <div
                        className="cursor-pointer border-2 border-gray-200 hover:border-purple-300 rounded-lg p-4 transition-all hover:shadow-lg"
                        onClick={() => applyTemplate('pastel')}
                      >
                        <div className="aspect-[3/4] bg-gradient-to-b from-pink-50 via-purple-50 to-blue-50 p-4 rounded-lg mb-3 flex flex-col items-center justify-center">
                          <div className="w-12 h-12 bg-gradient-to-r from-pink-200 to-purple-200 rounded-full mb-3"></div>
                          <div className="w-16 h-2 bg-purple-200 rounded mb-1"></div>
                          <div className="w-12 h-1 bg-pink-200 rounded mb-4"></div>
                          <div className="space-y-2 w-full">
                            <div className="w-full h-6 bg-gradient-to-r from-pink-200 to-purple-200 rounded"></div>
                            <div className="w-full h-6 bg-gradient-to-r from-purple-200 to-blue-200 rounded"></div>
                            <div className="w-full h-6 bg-gradient-to-r from-blue-200 to-cyan-200 rounded"></div>
                          </div>
                        </div>
                        <h3 className="font-semibold text-center">Pastel</h3>
                        <p className="text-sm text-gray-600 text-center">Soft and dreamy</p>
                      </div>
                    </div>

                    {/* Save Theme Section */}
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          onClick={() => setShowSaveDialog(true)}
                          className="w-full"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          Save Current Theme
                        </Button>
                      </div>

                      {showSaveDialog && (
                        <div className="mt-4 p-4 border rounded-lg bg-gray-50 space-y-3">
                          <div>
                            <Label htmlFor="theme-name">Theme Name</Label>
                            <Input
                              id="theme-name"
                              value={saveThemeName}
                              onChange={(e) => setSaveThemeName(e.target.value)}
                              placeholder="Enter theme name..."
                            />
                          </div>
                          <div>
                            <Label htmlFor="theme-description">Description (optional)</Label>
                            <Input
                              id="theme-description"
                              value={saveThemeDescription}
                              onChange={(e) => setSaveThemeDescription(e.target.value)}
                              placeholder="Describe your theme..."
                            />
                          </div>
                          <div className="flex gap-2">
                            <Button
                              onClick={handleSaveTheme}
                              disabled={!saveThemeName.trim() || isSaving}
                              className="flex-1"
                            >
                              <Heart className="w-4 h-4 mr-2" />
                              {isSaving ? 'Saving...' : 'Save Theme'}
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => setShowSaveDialog(false)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analytics">
                <Card>
                  <CardHeader className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white">
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle className="flex items-center">
                          <BarChart3 className="w-5 h-5 mr-2" />
                          Analytics Overview
                        </CardTitle>
                        <CardDescription className="text-indigo-100">
                          Track your link performance
                        </CardDescription>
                      </div>
                      <TabSaveButton
                        tabType="analytics"
                        variant="outline"
                        size="sm"
                        className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                        disabled={true}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center text-gray-500">
                      Analytics dashboard coming soon...
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Preview Panel */}
          <div className="lg:col-span-1">
            {/* Live Preview */}
            <div className="sticky top-24">
              <LivePreview
                profile={{
                  ...currentProfile,
                  avatar: currentProfile.avatar_url // Map avatar_url to avatar for LivePreview component
                }}
                links={(() => {
                  const mappedLinks = links.map(link => ({
                    id: link.id!,
                    title: link.title,
                    url: link.url,
                    description: link.description,
                    icon: link.icon || 'globe',
                    clicks: link.clicks,
                    isActive: link.isActive, // Use camelCase isActive (already converted in store)
                    backgroundColor: link.backgroundColor // Use camelCase backgroundColor (already converted in store)
                  }));
                  console.log('🔍 Dashboard - Raw links from store:', links);
                  console.log('🔍 Dashboard - Links count:', links.length);
                  console.log('🔍 Dashboard - Mapped links for LivePreview:', mappedLinks);
                  console.log('🔍 Dashboard - Active mapped links:', mappedLinks.filter(l => l.isActive));
                  return mappedLinks;
                })()}
                design={design}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
