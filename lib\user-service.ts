import { supabase } from './supabase';
import { TABLES } from './supabase';

export interface UserProfile {
  id?: string;
  user_id: string; // Supabase auth user ID
  name: string;
  username: string;
  email: string;
  bio: string;
  avatar_url: string; // Changed from avatar to avatar_url to match database
  avatarFile?: File; // Temporary file for upload, not persisted to database
  template: 'minimal' | 'dark' | 'neon' | 'pastel';
  custom_colors?: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
  };
  // Enhanced styling options
  background_image?: string; // URL to background image
  backgroundImageFile?: File; // Temporary file for upload, not persisted to database
  background_color?: string; // Background color
  background_type?: 'color' | 'image' | 'gradient' | 'none';
  gradientColors?: string[]; // For gradient backgrounds
  fontFamily?: string; // Custom font family
  fontSize?: 'small' | 'medium' | 'large';
  borderRadius?: 'none' | 'small' | 'medium' | 'large' | 'full';
  glassmorphism?: boolean; // Enable glassmorphism effects
  customCSS?: string; // Custom CSS for advanced users
  // Social links styling
  socialLinksStyle?: {
    position: 'top' | 'bottom' | 'sidebar';
    style: 'icons' | 'buttons' | 'minimal';
    size: 'small' | 'medium' | 'large';
  };
  // Link cards default styling
  defaultLinkStyle?: {
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    shadow?: boolean;
    hoverEffect?: 'none' | 'lift' | 'glow' | 'scale';
  };
  is_public: boolean;
  created_at?: string;
  updated_at?: string;
}

export class UserService {
  // Create a new user profile
  async createUserProfile(authUser: any): Promise<UserProfile> {
    try {
      console.log('📝 Auth user object:', authUser);

      // Get the current authenticated user to ensure we have the right ID
      const { data: { user: currentUser }, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.error('❌ Failed to get current user:', authError);
        throw authError;
      }

      if (!currentUser) {
        throw new Error('No authenticated user found');
      }

      console.log('📝 Current authenticated user:', currentUser);

      // Generate a unique username from email or name
      const baseUsername = this.generateUsername(authUser.email || authUser.name || currentUser.email);
      const username = await this.ensureUniqueUsername(baseUsername);

      const profileData = {
        user_id: currentUser.id, // Use the authenticated user's ID
        name: authUser.name || currentUser.user_metadata?.name || currentUser.email?.split('@')[0] || 'User',
        username,
        email: authUser.email || currentUser.email || '',
        bio: '',
        avatar_url: '',
        template: 'minimal' as const,
        is_public: true
      };

      console.log('📝 Creating user profile with data:', profileData);

      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .insert(profileData)
        .select()
        .single();

      if (error) {
        console.error('❌ Supabase error creating user profile:', error);
        console.error('❌ Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      console.log('✅ User profile created successfully:', data);
      return data as UserProfile;
    } catch (error) {
      console.error('Failed to create user profile:', error);
      throw error;
    }
  }

  // Get user profile by auth user ID
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw error;
      }

      return data as UserProfile;
    } catch (error) {
      console.error('Failed to get user profile:', error);
      return null;
    }
  }

  // Get user profile by email
  async getUserProfileByEmail(email: string): Promise<UserProfile | null> {
    try {
      console.log('🔍 Searching for user by email:', email);

      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .select('*')
        .eq('email', email);

      if (error) throw error;

      console.log(`📊 Found ${data?.length || 0} users with email ${email}`);

      if (data && data.length > 1) {
        console.warn(`⚠️ Multiple users found with email ${email}:`, data.map(doc => ({
          id: doc.id,
          username: doc.username,
          created_at: doc.created_at
        })));
      }

      if (data && data.length > 0) {
        const profile = data[0] as UserProfile;
        console.log('✅ Returning user profile:', {
          id: profile.id,
          username: profile.username,
          email: profile.email
        });
        return profile;
      }

      console.log('❌ No user found with email:', email);
      return null;
    } catch (error) {
      console.error('💥 Failed to get user profile by email:', error);
      return null;
    }
  }

  // Get user profile by username
  async getUserProfileByUsername(username: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .select('*')
        .eq('username', username)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw error;
      }

      return data as UserProfile;
    } catch (error) {
      console.error('Failed to get user profile by username:', error);
      return null;
    }
  }

  // Update user profile
  async updateUserProfile(profileId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const updateData = { ...updates };

      // Remove fields that shouldn't be updated
      delete updateData.id;
      delete updateData.user_id;
      delete updateData.created_at;
      delete updateData.updated_at;

      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .update(updateData)
        .eq('id', profileId)
        .select()
        .single();

      if (error) throw error;
      return data as UserProfile;
    } catch (error) {
      console.error('❌ Failed to update user profile:', error);
      console.error('❌ Error details:', JSON.stringify(error, null, 2));
      console.error('❌ Update data was:', JSON.stringify(updates, null, 2));
      throw error;
    }
  }

  // Delete user profile
  async deleteUserProfile(profileId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from(TABLES.PROFILES)
        .delete()
        .eq('id', profileId);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to delete user profile:', error);
      throw error;
    }
  }

  // Check if username is available
  async isUsernameAvailable(username: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .select('id')
        .eq('username', username);

      if (error) throw error;
      return !data || data.length === 0;
    } catch (error) {
      console.error('Failed to check username availability:', error);
      return false;
    }
  }

  // Generate username from email or name
  private generateUsername(emailOrName: string): string {
    let base = emailOrName;
    
    // If it's an email, use the part before @
    if (emailOrName.includes('@')) {
      base = emailOrName.split('@')[0];
    }
    
    // Clean up the username: remove special characters, convert to lowercase
    base = base
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 20); // Limit length
    
    // Ensure it's not empty
    if (!base) {
      base = 'user';
    }
    
    return base;
  }

  // Ensure username is unique by adding numbers if needed
  private async ensureUniqueUsername(baseUsername: string): Promise<string> {
    let username = baseUsername;
    let counter = 1;
    
    while (!(await this.isUsernameAvailable(username))) {
      username = `${baseUsername}${counter}`;
      counter++;
      
      // Prevent infinite loop
      if (counter > 1000) {
        username = `${baseUsername}${Date.now()}`;
        break;
      }
    }
    
    return username;
  }

  // Get or create user profile (useful for OAuth flows)
  async getOrCreateUserProfile(authUser: any): Promise<UserProfile> {
    try {
      // Get the current authenticated user to ensure we have the right ID
      const { data: { user: currentUser }, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.error('❌ Failed to get current user:', authError);
        throw authError;
      }

      if (!currentUser) {
        throw new Error('No authenticated user found');
      }

      console.log('🔍 getOrCreateUserProfile called for:', {
        authUserId: authUser?.id,
        currentUserId: currentUser.id,
        email: authUser?.email || currentUser.email,
        name: authUser?.name || currentUser.user_metadata?.name
      });

      // First, try to get existing profile by current auth user ID
      let profile = await this.getUserProfile(currentUser.id);

      if (profile) {
        console.log('✅ Found existing profile by userId:', currentUser.email);
        return profile;
      }

      // If no profile by userId, check by email to prevent duplicates
      const email = authUser?.email || currentUser.email;
      if (email) {
        console.log('🔍 Checking for existing profile by email:', email);
        profile = await this.getUserProfileByEmail(email);

        if (profile) {
          console.log('✅ Found existing profile by email, updating userId:', email);
          // Update the existing profile with the current auth user ID
          profile = await this.updateUserProfile(profile.id!, {
            user_id: currentUser.id,
            name: authUser?.name || currentUser.user_metadata?.name || profile.name
          });
          return profile;
        } else {
          console.log('❌ No existing profile found by email:', email);
        }
      } else {
        console.log('⚠️ No email provided for user:', currentUser.id);
      }

      // If no profile exists by either userId or email, create a new one
      console.log('🆕 Creating new user profile for:', email);
      profile = await this.createUserProfile(authUser || currentUser);

      return profile;
    } catch (error) {
      console.error('💥 Failed to get or create user profile:', error);
      throw error;
    }
  }

  // Find and merge duplicate users by email
  async findAndMergeDuplicateUsers(email: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .select('*')
        .eq('email', email)
        .order('created_at', { ascending: true });

      if (error) throw error;

      if (!data || data.length <= 1) {
        return data?.[0] as UserProfile || null;
      }

      console.log(`Found ${data.length} duplicate users for email: ${email}`);

      // Keep the oldest profile (first created)
      const primaryProfile = data[0];
      const duplicateProfiles = data.slice(1);

      // Delete duplicate profiles
      for (const duplicate of duplicateProfiles) {
        try {
          await this.deleteUserProfile(duplicate.id!);
          console.log(`Deleted duplicate profile: ${duplicate.id}`);
        } catch (error) {
          console.error(`Failed to delete duplicate profile ${duplicate.id}:`, error);
        }
      }

      return primaryProfile as UserProfile;
    } catch (error) {
      console.error('Failed to find and merge duplicate users:', error);
      return null;
    }
  }

  // Search users by username or name
  async searchUsers(query: string, limit: number = 10): Promise<UserProfile[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .select('*')
        .eq('is_public', true)
        .or(`username.ilike.%${query}%,name.ilike.%${query}%`)
        .limit(limit);

      if (error) throw error;
      return data as UserProfile[] || [];
    } catch (error) {
      console.error('Failed to search users:', error);
      return [];
    }
  }

  // Save complete profile with all styling and customization
  async saveCompleteProfile(userId: string, profileData: {
    // Basic profile info
    name?: string;
    bio?: string;
    avatar?: string;
    template?: string;

    // Colors and styling
    customColors?: any;
    backgroundImage?: string;
    backgroundColor?: string;
    backgroundType?: string;
    gradientColors?: string[];
    fontFamily?: string;
    fontSize?: string;
    borderRadius?: string;
    glassmorphism?: boolean;
    customCSS?: string;

    // Social links styling
    socialLinksStyle?: any;

    // Default link styling
    defaultLinkStyle?: any;
  }): Promise<UserProfile> {
    try {
      // Get existing profile or create one if it doesn't exist
      let existingProfile = await this.getUserProfile(userId);

      if (!existingProfile) {
        console.log('🆕 Profile not found, creating new profile for user:', userId);
        existingProfile = await this.getOrCreateUserProfile({ id: userId });
      }

      // Prepare update data with only basic fields that exist in all collections
      const updateData: any = {};

      // Only add basic fields that should exist
      if (profileData.name !== undefined) updateData.name = profileData.name;
      if (profileData.bio !== undefined) updateData.bio = profileData.bio;
      if (profileData.avatar_url !== undefined) updateData.avatar_url = profileData.avatar_url;
      if (profileData.template !== undefined) updateData.template = profileData.template;

      // Handle customColors (this should exist in most setups)
      if (profileData.customColors !== undefined) {
        updateData.custom_colors = profileData.customColors;
      }

      // Only add enhanced fields if they're provided (they might not exist in the collection yet)
      const enhancedFieldMapping = {
        'backgroundImage': 'background_image',
        'backgroundColor': 'background_color',
        'backgroundType': 'background_type',
        'fontFamily': 'font_family',
        'fontSize': 'font_size',
        'borderRadius': 'border_radius',
        'glassmorphism': 'glassmorphism',
        'customCSS': 'custom_css'
      };

      Object.entries(enhancedFieldMapping).forEach(([oldField, newField]) => {
        if (profileData[oldField] !== undefined) {
          updateData[newField] = profileData[oldField];
        }
      });

      // Handle JSON fields carefully
      if (profileData.gradientColors !== undefined) {
        updateData.gradient_colors = profileData.gradientColors;
      }
      if (profileData.socialLinksStyle !== undefined) {
        updateData.social_links_style = profileData.socialLinksStyle;
      }
      if (profileData.defaultLinkStyle !== undefined) {
        updateData.default_link_style = profileData.defaultLinkStyle;
      }

      // Update the profile
      const result = await this.updateUserProfile(existingProfile.id!, updateData);

      // Supabase handles JSON automatically, no need to parse

      return result;
    } catch (error) {
      console.error('❌ Failed to save complete profile:', error);
      console.error('❌ Error details:', JSON.stringify(error, null, 2));
      console.error('❌ Profile data was:', JSON.stringify(profileData, null, 2));
      console.error('❌ User ID was:', userId);
      throw error;
    }
  }

  // Get complete profile with parsed JSON fields
  async getCompleteProfile(userId: string): Promise<UserProfile | null> {
    try {
      const profile = await this.getUserProfile(userId);
      // Supabase handles JSON automatically, no need to parse
      return profile;
    } catch (error) {
      console.error('Failed to get complete profile:', error);
      return null;
    }
  }

  // Get public user profiles
  async getPublicUsers(limit: number = 20): Promise<UserProfile[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .select('*')
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data as UserProfile[] || [];
    } catch (error) {
      console.error('Failed to get public users:', error);
      return [];
    }
  }
}

// Export singleton instance
export const userService = new UserService();
